---
alwaysApply: true
---

# Python项目开发规范

## 一、基本规范

1. **语言使用**：所有回复和代码使用中文，包括变量、函数、注释
2. **系统函数**：Python内置函数不要使用中文，以免造成程序出错
3. **称呼规范**：对我的称呼用"靓仔"
4. **开发者水平**：我是python的初学者,不要使用多态,继承,工厂模式等一些复杂的架构,不是使用链式调用等方法,要贴合python的初学者进行规划

## 二、项目结构规范（模块化设计）

### 2.1 简单模块化原则

1. **功能分离**：每个文件只做一件事
2. **简单明了**：代码容易理解和修改
3. **文件管理**：用文件夹来组织相关文件


## 三、命名规范

### 3.1 核心命名规则

| 变量类型     | 前缀  | 示例            | 说明                     |
|-------------|-------|----------------|--------------------------|
| **全局变量** | `全_` | `全_数据库连接` | 整个程序都能访问的变量    |
| **类的变量** | `类_` | `类_用户计数`   | 类中定义的变量           |
| **参数**     | `参_` | `参_用户名`     | 函数的输入参数           |
| **局部变量** | 无前缀 | `用户名`       | 函数内部使用的变量       |

### 3.2 文件和类命名

```python
# 文件命名
类_主窗口.py              # 类文件以"类_"开头
数据库管理.py             # 功能文件直接用功能名

# 类命名
class 类_主窗口:          # 类名以"类_"开头
class 类_数据库管理:      # 清晰表达类的功能
```

### 3.3 数据类型命名

```python
列表_用户数据 = []        # 列表类型
字典_配置信息 = {}        # 字典类型
集合_唯一值 = set()       # 集合类型
元组_坐标点 = ()          # 元组类型
```

## 四、代码组织规范

### 4.1 类内方法组织顺序

```python
class 类_示例:
    """类的说明"""
    
    # 1. 类变量
    类_版本 = "1.0"
    
    # 2. 初始化方法
    def __init__(self):
        pass
    
    # 3. 公共方法（按功能分组）
    def 获取数据(self):
        pass
    
    def 保存数据(self):
        pass
    
    # 4. 事件处理方法
    def 槽_按钮点击(self):
        pass
    
    # 5. 私有方法
    def _内部处理(self):
        pass
    
    # 6. 静态方法和类方法
    @staticmethod
    def 工具方法():
        pass
```


## 五、错误处理规范

```python
def 安全执行方法(self, 参_操作参数):
    """带异常处理的方法示例"""
    try:
        # 主要业务逻辑
        结果 = self.执行业务逻辑(参_操作参数)
        logging.info(f"[{self.__class__.__name__}] 业务逻辑执行成功")
        return 结果
        
    except ValueError as e:
        logging.warning(f"[{self.__class__.__name__}] 参数错误: {e}")
        return None
        
    except Exception as e:
        logging.error(f"[{self.__class__.__name__}] 执行过程中发生未知错误", exc_info=True)
        return None
```




## 六、模块间通信规范

```python
from PySide6.QtCore import Signal, QObject

class 类_事件管理(QObject):
    """事件管理类，负责模块间通信"""
    
    信号_数据更新 = Signal(str, dict)
    信号_状态变化 = Signal(str)
    
    def 发送数据更新信号(self, 参_数据类型, 参_数据):
        """发送数据更新信号"""
        self.信号_数据更新.emit(参_数据类型, 参_数据)
```

## 七、函数备注规范

### 7.1 基本备注格式

```python
def 函数名(self, 参_参数1: str, 参_参数2: int = 0) -> dict:
    """
    函数功能简述（一句话描述）
    
    详细描述（可选，当功能复杂时需要）
    说明函数的具体作用、使用场景等
    
    参数:
        参_参数1 (str): 参数说明
        参_参数2 (int, 可选): 参数说明，默认值说明
        
    返回:
        dict: 返回值说明
        
    异常:
        异常类型: 什么情况下会抛出此异常
        
    示例:
        使用示例代码（可选）
    """
    pass
```

### 7.2 常见函数备注示例

#### 7.2.1 初始化方法
```python
def __init__(self, 参_配置路径: str, 参_数据库连接: object = None) -> None:
    """
    初始化类实例
    
    参数:
        参_配置路径 (str): 配置文件路径
        参_数据库连接 (object, 可选): 数据库连接对象，默认为None时会创建新连接
    """
    pass
```

#### 7.2.2 数据获取方法
```python
def 获取用户数据(self, 参_用户ID: int, 参_数据类型: str = "全部") -> dict:
    """
    根据用户ID获取用户数据
    
    参数:
        参_用户ID (int): 用户唯一标识
        参_数据类型 (str, 可选): 数据类型筛选，可选值："全部"、"基本信息"、"交易记录"，默认为"全部"
        
    返回:
        dict: 用户数据字典，包含用户信息和相关数据
        None: 当用户不存在时返回None
        
    异常:
        ValueError: 当用户ID格式不正确时抛出
        ConnectionError: 当数据库连接失败时抛出
    """
    pass
```

#### 7.2.3 复杂业务方法
```python
def 计算交易信号(self, 参_K线数据: list, 参_策略参数: dict, 参_时间范围: str = "1小时") -> dict:
    """
    根据K线数据和策略参数计算交易信号
    
    此函数是量化交易的核心算法，通过分析历史价格数据，
    结合技术指标和策略参数，生成买入或卖出信号。
    
    参数:
        参_K线数据 (list): K线数据列表，每个元素包含时间、开盘价、最高价、最低价、收盘价、成交量
        参_策略参数 (dict): 策略参数字典，包含各种技术指标参数
        参_时间范围 (str, 可选): 分析的时间范围，默认为"1小时"
        
    返回:
        dict: 交易信号字典，包含：
            - 信号类型 (str): "买入"、"卖出"、"持有"
            - 信号强度 (float): 信号强度值，范围0-1
            - 建议价格 (float): 建议交易价格
            - 置信度 (float): 信号置信度，范围0-1
            - 理由 (str): 生成信号的理由说明
            
    异常:
        ValueError: 当K线数据格式不正确或策略参数缺失时抛出
        TypeError: 当参数类型不正确时抛出
    """
    pass
```

### 7.3 备注编写要点

1. **类型标注**：参数和返回值都要标注类型
2. **信息完整**：必须包含参数说明、返回值说明、异常说明
3. **格式统一**：保持一致的缩进和格式
4. **中文优先**：所有说明文字使用中文

## 八、日志记录规范

### 8.1 日志级别使用

```python
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_机器人数据访问:
    def __init__(self):
        logger.info("机器人数据访问类初始化完成")

    def 保存配置(self):
        logger.info("保存配置成功")
```

