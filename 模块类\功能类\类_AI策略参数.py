"""
AI策略参数类
用于存储AI策略的固定参数配置
"""

from typing import Dict, List, Optional, Union, Any

class 类_AI策略参数:
    """
    AI策略参数类
    用于存储AI策略的固定参数配置
    """
    
    @staticmethod
    def 获取进攻型参数(参_子策略: str) -> Optional[Dict[str, Any]]:
        """
        获取进攻型策略参数
        
        参数:
            参_子策略: 子策略名称
            
        返回:
            参数配置字典，如果未找到则返回None
        """
        参数配置 = {
            "疯狂型": {
                # 基础参数
                "最大持仓单数": 20,
                "持仓倍数": 1.3,
                "止盈阈值": 0.012,
                # 复利配置
                "复利计算": True,
                "复利分母": 631,
                "动态间距": True,
                # 止盈配置
                "动态回撤比例": 0.2,
                # ATR参数
                "ATR时间周期": "5m",
                "ATR计算周期": 14,
                "ATR间距倍数": 1.5,
                "ATR反弹因子": 0.15,
                "ATR反弹K线数": 3,
                "ATR检查周期": "1m",
                "ATR反弹补单": True
            },
            "激进型": {
                # 基础参数
                "最大持仓单数": 20,
                "持仓倍数": 1.3,
                "止盈阈值": 0.012,
                # 复利配置
                "复利计算": True,
                "复利分母": 631,
                "动态间距": True,
                # 止盈配置
                "动态回撤比例": 0.2,
                # ATR参数
                "ATR时间周期": "5m",
                "ATR计算周期": 14,
                "ATR间距倍数": 2,
                "ATR反弹因子": 0.15,
                "ATR反弹K线数": 3,
                "ATR检查周期": "1m",
                "ATR反弹补单": True
            },
            "平衡型": {
                # 基础参数
                "最大持仓单数": 20,
                "持仓倍数": 1.3,
                "止盈阈值": 0.012,
                # 复利配置
                "复利计算": True,
                "复利分母": 631,
                "动态间距": True,
                # 止盈配置
                "动态回撤比例": 0.2,
                # ATR参数
                "ATR时间周期": "15m",
                "ATR计算周期": 14,
                "ATR间距倍数": 1,
                "ATR反弹因子": 0.15,
                "ATR反弹K线数": 3,
                "ATR检查周期": "1m",
                "ATR反弹补单": True
            },
            "稳健型": {
              # 基础参数
                "最大持仓单数": 20,
                "持仓倍数": 1.3,
                "止盈阈值": 0.012,
                # 复利配置
                "复利计算": True,
                "复利分母": 631,
                "动态间距": True,
                # 止盈配置
                "动态回撤比例": 0.2,
                # ATR参数
                "ATR时间周期": "30m",
                "ATR计算周期": 14,
                "ATR间距倍数": 1,
                "ATR反弹因子": 0.15,
                "ATR反弹K线数": 3,
                "ATR检查周期": "1m",
                "ATR反弹补单": True
            },
            "保守型": {
                # 基础参数
                "最大持仓单数": 20,
                "持仓倍数": 1.3,
                "止盈阈值": 0.012,
                # 复利配置
                "复利计算": True,
                "复利分母": 631,
                "动态间距": True,
                # 止盈配置
                "动态回撤比例": 0.2,
                # ATR参数
                "ATR时间周期": "1H",
                "ATR计算周期": 14,
                "ATR间距倍数": 1,
                "ATR反弹因子": 0.15,
                "ATR反弹K线数": 3,
                "ATR检查周期": "1m",
                "ATR反弹补单": True
            }
        }
        return 参数配置.get(参_子策略)
    
    @staticmethod
    def 获取防守型参数(参_子策略: str) -> Optional[Dict[str, Any]]:
        """
        获取防守型策略参数
        
        参数:
            参_子策略: 子策略名称
            
        返回:
            参数配置字典，如果未找到则返回None
        """
        参数配置 = {
            "疯狂型": {
                   # 基础参数
                "最大持仓单数": 16,
                "持仓倍数": 1.3,
                "止盈阈值": 0.012,
                # 复利配置
                "复利计算": True,
                "复利分母": 219,
                "动态间距": True,
                # 止盈配置
                "动态回撤比例": 0.2,
                # ATR参数
                "ATR时间周期": "15m",
                "ATR计算周期": 14,
                "ATR间距倍数": 1,
                "ATR反弹因子": 0.15,
                "ATR反弹K线数": 3,
                "ATR检查周期": "1m",
                "ATR反弹补单": True,
                # CCI参数
                "CCI功能开关": True,
                "主周期时间框架": "3m",
                "主周期计算期数": 14,
                "主周期阈值": 1,
                "主周期K线数": 1,
                "辅助周期时间框架": "5m",
                "辅助周期计算期数": 14,
                "辅助周期阈值": 1,
                "辅助周期K线数": 1,
                "CCI趋势下单": True
            },
            "激进型": {
                    # 基础参数
                "最大持仓单数": 20,
                "持仓倍数": 1.3,
                "止盈阈值": 0.012,
                # 复利配置
                "复利计算": True,
                "复利分母": 631,
                "动态间距": True,
                # 止盈配置
                "动态回撤比例": 0.2,
                # ATR参数
                "ATR时间周期": "3m",
                "ATR计算周期": 14,
                "ATR间距倍数": 1,
                "ATR反弹因子": 0.3,
                "ATR反弹K线数": 3,
                "ATR检查周期": "1m",
                "ATR反弹补单": True,
                # CCI参数
                "CCI功能开关": True,
                "主周期时间框架": "1m",
                "主周期计算期数": 14,
                "主周期阈值": 1,
                "主周期K线数": 1,
                "辅助周期时间框架": "3m",
                "辅助周期计算期数": 14,
                "辅助周期阈值": 1,
                "辅助周期K线数": 1,
                "CCI趋势下单": True
                
            },
            "平衡型": {
                        # 基础参数
                "最大持仓单数": 20,
                "持仓倍数": 1.3,
                "止盈阈值": 0.012,
                # 复利配置
                "复利计算": True,
                "复利分母": 631,
                "动态间距": True,
                # 止盈配置
                "动态回撤比例": 0.2,
                # ATR参数
                "ATR时间周期": "5m",
                "ATR计算周期": 14,
                "ATR间距倍数": 1,
                "ATR反弹因子": 0.3,
                "ATR反弹K线数": 3,
                "ATR检查周期": "1m",
                "ATR反弹补单": True,
                # CCI参数
                "CCI功能开关": True,
                "主周期时间框架": "1m",
                "主周期计算期数": 14,
                "主周期阈值": 1,
                "主周期K线数": 1,
                "辅助周期时间框架": "3m",
                "辅助周期计算期数": 14,
                "辅助周期阈值": 1,
                "辅助周期K线数": 1,
                "CCI趋势下单": True
            },
            "稳健型": {
                # 基础参数
                "最大持仓单数": 20,
                "持仓倍数": 1.3,
                "止盈阈值": 0.012,
                # 复利配置
                "复利计算": True,
                "复利分母": 631,
                "动态间距": True,
                # 止盈配置
                "动态回撤比例": 0.2,
                # ATR参数
                "ATR时间周期": "15m",
                "ATR计算周期": 14,
                "ATR间距倍数": 1,
                "ATR反弹因子": 0.15,
                "ATR反弹K线数": 3,
                "ATR检查周期": "1m",
                "ATR反弹补单": True,
                # CCI参数
                "CCI功能开关": True,
                "主周期时间框架": "1m",
                "主周期计算期数": 14,
                "主周期阈值": 1,
                "主周期K线数": 1,
                "辅助周期时间框架": "3m",
                "辅助周期计算期数": 14,
                "辅助周期阈值": 1,
                "辅助周期K线数": 1,
                "CCI趋势下单": True
            },
            "保守型": {
                # 基础参数
                "最大持仓单数": 20,
                "持仓倍数": 1.3,
                "止盈阈值": 0.012,
                # 复利配置
                "复利计算": True,
                "复利分母": 631,
                "动态间距": True,
                # 止盈配置
                "动态回撤比例": 0.2,
                # ATR参数
                "ATR时间周期": "30m",
                "ATR计算周期": 14,
                "ATR间距倍数": 1,
                "ATR反弹因子": 0.15,
                "ATR反弹K线数": 3,
                "ATR检查周期": "1m",
                "ATR反弹补单": True,
                # CCI参数
                "CCI功能开关": True,
                "主周期时间框架": "1m",
                "主周期计算期数": 14,
                "主周期阈值": 1,
                "主周期K线数": 1,
                "辅助周期时间框架": "3m",
                "辅助周期计算期数": 14,
                "辅助周期阈值": 1,
                "辅助周期K线数": 1,
                "CCI趋势下单": True
            }
        }
        return 参数配置.get(参_子策略)
    
    @staticmethod
    def 获取通用型参数(参_子策略: str) -> Optional[Dict[str, Any]]:
        """
        获取通用型策略参数
        
        参数:
            参_子策略: 子策略名称
            
        返回:
            参数配置字典，如果未找到则返回None
        """
        参数配置 = {
            "A": {
                # 基础参数
                "最大持仓单数": 12,
                "持仓倍数": 1.366,
                "止盈阈值": 0.012,
                # 复利配置
                "复利计算": True,
                "复利分母": 113,
                "动态间距": True,
                # 止盈配置
                "动态回撤比例": 0.2,
                # ATR参数
                "ATR时间周期": "15m",
                "ATR计算周期": 14,
                "ATR间距倍数": 1.5,
                "ATR反弹因子": 0.15,
                "ATR反弹K线数": 3,
                "ATR检查周期": "1m",
                "ATR反弹补单": True,
                # CCI参数
                "CCI功能开关": True,
                "主周期时间框架": "15m",
                "主周期计算期数": 5,
                "主周期阈值": 1,
                "主周期K线数": 1,
                "辅助周期时间框架": "15m",
                "辅助周期计算期数": 14,
                "辅助周期阈值": 1,
                "辅助周期K线数": 1,
                "CCI趋势下单": True
            }
        }
        return 参数配置.get(参_子策略)
    
    @staticmethod
    def 获取策略参数(参_策略类型: str, 参_子策略: str) -> Optional[Dict[str, Any]]:
        """
        获取指定策略类型和子策略的参数配置
        
        参数:
            参_策略类型: 策略类型（进攻型、防守型、通用型）
            参_子策略: 子策略名称
            
        返回:
            参数配置字典，如果未找到则返回None
        """
        if 参_策略类型 == "进攻型":
            return 类_AI策略参数.获取进攻型参数(参_子策略)
        elif 参_策略类型 == "防守型":
            return 类_AI策略参数.获取防守型参数(参_子策略)
        elif 参_策略类型 == "通用型":
            return 类_AI策略参数.获取通用型参数(参_子策略)
        return None
    
    @staticmethod
    def 获取所有策略类型() -> List[str]:
        """
        获取所有可用的策略类型
        
        返回:
            策略类型列表
        """
        return ["进攻型", "防守型", "通用型"]
    
    @staticmethod
    def 获取子策略列表(参_策略类型: str) -> List[str]:
        """
        获取指定策略类型下的所有子策略
        
        参数:
            参_策略类型: 策略类型
            
        返回:
            子策略列表
        """
        子策略映射 = {
            "进攻型": ["疯狂型", "激进型", "平衡型"],
            "防守型": ["稳健型", "保守型"],
            "通用型": ["A"]
        }
        return 子策略映射.get(参_策略类型, []) 