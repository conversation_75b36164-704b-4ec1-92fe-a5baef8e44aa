"""
表结构管理类
负责创建和更新数据库表结构
"""

import sqlite3
import logging
from typing import Optional
from .类_数据库连接管理 import 类_数据库连接管理

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_表结构管理:
    """
    表结构管理类
    负责创建和更新数据库表结构
    """
    
    def __init__(self, 参_数据库连接管理: Optional[类_数据库连接管理] = None):
        """
        初始化表结构管理类
        
        参数:
            参_数据库连接管理: 数据库连接管理实例
        """
        self.类_数据库连接管理 = 参_数据库连接管理 or 类_数据库连接管理()
        self.类_模块名 = "表结构管理"
        
        # 初始化数据库表结构
        self.初始化数据库()
        
        logger.info("表结构管理类初始化完成")
        print("表结构管理类初始化完成")
    
    def 初始化数据库(self):
        """
        初始化数据库，创建所有必要的表
        """
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        try:
            # 创建中控表
            self.创建中控表(游标)
            
            # 创建持仓表
            self.创建持仓表(游标)
            
            # 创建平仓表
            self.创建平仓表(游标)
            
            # 创建API配置表
            self.创建API配置表(游标)
            
            # 创建系统配置表
            self.创建系统配置表(游标)
            
            # 创建开平仓表
            self.创建开平仓表(游标)
            
            # 创建交易对配置表
            self.创建交易对配置表(游标)
            
            # 检查并更新数据库结构
            self.检查并更新数据库结构(游标)
            
            # 提交事务
            连接.commit()
            logger.info("数据库表创建完成")
            print("数据库表创建完成")
            
        except Exception as e:
            连接.rollback()
            logger.error(f"数据库初始化失败: {e}", exc_info=True)
            print(f"数据库初始化失败: {e}")
            raise
    
    def 检查并更新数据库结构(self, 参_游标: sqlite3.Cursor):
        """
        检查并更新数据库结构
        如果有新的表结构变更，在这里添加相应的代码
        
        参数:
            参_游标: 数据库游标
        """
        try:
            # 检查中控表是否存在策略参数JSON字段
            参_游标.execute("PRAGMA table_info(中控表)")
            列信息 = 参_游标.fetchall()
            列名列表 = [列[1] for 列 in 列信息]
            
            # 如果中控表没有策略参数JSON字段，添加它
            if "策略参数JSON" not in 列名列表:
                参_游标.execute("ALTER TABLE 中控表 ADD COLUMN 策略参数JSON TEXT")
                logger.info("中控表添加策略参数JSON字段")
                print("中控表添加策略参数JSON字段")
                
        except Exception as e:
            logger.error(f"检查并更新数据库结构失败: {e}", exc_info=True)
            print(f"检查并更新数据库结构失败: {e}")
            raise
    
    def 创建中控表(self, 参_游标: sqlite3.Cursor):
        """
        创建中控表
        
        参数:
            参_游标: 数据库游标
        """
        创建表SQL = """
        CREATE TABLE IF NOT EXISTS 中控表 (
            序号 INTEGER PRIMARY KEY AUTOINCREMENT,
            交易对 TEXT NOT NULL,
            预配本金 REAL DEFAULT 0,
            预配首单 REAL DEFAULT 0,
            本次下单 REAL DEFAULT 0,
            复利金额 REAL DEFAULT 0,
            累计下单 REAL DEFAULT 0,
            订单数量 INTEGER DEFAULT 0,
            最大订单 INTEGER DEFAULT 0,
            浮动收益 REAL DEFAULT 0,
            浮动收益率 REAL DEFAULT 0,
            结算收益 REAL DEFAULT 0,
            持仓均价 REAL DEFAULT 0,
            持仓数量 REAL DEFAULT 0,
            预估平仓价格 REAL DEFAULT 0,
            预估月化率 REAL DEFAULT 0,
            预估年化率 REAL DEFAULT 0,
            策略状态 TEXT DEFAULT '停止',
            当前状态 TEXT DEFAULT '待机',
            策略配置 TEXT DEFAULT '自定义',
            策略类型 TEXT DEFAULT '通用型',
            子策略 TEXT DEFAULT '默认策略',
            策略参数JSON TEXT,
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            机器人编号 TEXT UNIQUE NOT NULL
        )
        """
        参_游标.execute(创建表SQL)
    
    def 创建持仓表(self, 参_游标: sqlite3.Cursor):
        """
        创建持仓表
        
        参数:
            参_游标: 数据库游标
        """
        创建表SQL = """
        CREATE TABLE IF NOT EXISTS 持仓表 (
            序号 INTEGER PRIMARY KEY AUTOINCREMENT,
            交易对 TEXT NOT NULL,
            开仓蜡烛时间 DATETIME,
            开仓时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            开仓金额 REAL NOT NULL,
            开仓数量 REAL NOT NULL,
            开仓手续费 REAL DEFAULT 0,
            策略配置 TEXT DEFAULT '自定义',
            策略类型 TEXT DEFAULT '通用型',
            子策略 TEXT DEFAULT '默认策略',
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            机器人编号 TEXT NOT NULL,
            FOREIGN KEY (机器人编号) REFERENCES 中控表(机器人编号)
        )
        """
        参_游标.execute(创建表SQL)
    
    def 创建平仓表(self, 参_游标: sqlite3.Cursor):
        """
        创建平仓表
        
        参数:
            参_游标: 数据库游标
        """
        创建表SQL = """
        CREATE TABLE IF NOT EXISTS 平仓表 (
            序号 INTEGER PRIMARY KEY AUTOINCREMENT,
            交易对 TEXT NOT NULL,
            开仓蜡烛时间 DATETIME,
            开仓时间 DATETIME,
            开仓金额 REAL NOT NULL,
            开仓数量 REAL NOT NULL,
            开仓手续费 REAL DEFAULT 0,
            平仓蜡烛时间 DATETIME,
            平仓时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            平仓金额 REAL NOT NULL,
            平仓数量 REAL NOT NULL,
            平仓收益 REAL DEFAULT 0,
            平仓收益率 REAL DEFAULT 0,
            平仓手续费 REAL DEFAULT 0,
            策略配置 TEXT DEFAULT '自定义',
            策略类型 TEXT DEFAULT '通用型',
            子策略 TEXT DEFAULT '默认策略',
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            机器人编号 TEXT NOT NULL,
            FOREIGN KEY (机器人编号) REFERENCES 中控表(机器人编号)
        )
        """
        参_游标.execute(创建表SQL)
    
    def 创建API配置表(self, 参_游标: sqlite3.Cursor):
        """
        创建API配置表
        
        参数:
            参_游标: 数据库游标
        """
        创建表SQL = """
        CREATE TABLE IF NOT EXISTS API配置表 (
            序号 INTEGER PRIMARY KEY AUTOINCREMENT,
            交易所名称 TEXT UNIQUE NOT NULL,
            api_key TEXT NOT NULL,
            secret_key TEXT NOT NULL,
            passphrase TEXT,
            是否启用 BOOLEAN DEFAULT 1,
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        参_游标.execute(创建表SQL)
    
    def 创建系统配置表(self, 参_游标: sqlite3.Cursor):
        """
        创建系统配置表
        
        参数:
            参_游标: 数据库游标
        """
        创建表SQL = """
        CREATE TABLE IF NOT EXISTS 系统配置表 (
            序号 INTEGER PRIMARY KEY AUTOINCREMENT,
            配置键 TEXT UNIQUE NOT NULL,
            配置值 TEXT NOT NULL,
            配置描述 TEXT,
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        参_游标.execute(创建表SQL)
        
        # 插入默认配置
        默认配置列表 = [
            ('当前交易所', 'OKX', '当前选择的交易所'),
            ('下单模式', '本地模拟', '当前下单模式'),
            ('自动更新间隔', '5', '数据自动更新间隔(秒)'),
            ('默认首单金额', '100', '默认首单金额(USDT)'),
            ('风险控制等级', '中等', '风险控制等级')
        ]
        
        for 配置键, 配置值, 配置描述 in 默认配置列表:
            参_游标.execute(
                "INSERT OR IGNORE INTO 系统配置表 (配置键, 配置值, 配置描述) VALUES (?, ?, ?)",
                (配置键, 配置值, 配置描述)
            )
    
    def 创建开平仓表(self, 参_游标: sqlite3.Cursor):
        """
        创建开平仓表
        
        参数:
            参_游标: 数据库游标
        """
        创建表SQL = """
        CREATE TABLE IF NOT EXISTS 开平仓表 (
            序号 INTEGER PRIMARY KEY AUTOINCREMENT,
            交易对 TEXT NOT NULL,
            机器人编号 TEXT NOT NULL,
            订单类型 TEXT NOT NULL,
            蜡烛时间 DATETIME,
            开平仓时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            金额 REAL NOT NULL,
            数量 REAL NOT NULL,
            手续费 REAL DEFAULT 0,
            收益 REAL DEFAULT 0,
            收益率 REAL DEFAULT 0,
            策略配置 TEXT DEFAULT '自定义',
            策略类型 TEXT DEFAULT '通用型',
            子策略 TEXT DEFAULT '默认策略',
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (机器人编号) REFERENCES 中控表(机器人编号)
        )
        """
        参_游标.execute(创建表SQL)
    
    def 创建交易对配置表(self, 参_游标: sqlite3.Cursor):
        """
        创建交易对配置表
        
        参数:
            参_游标: 数据库游标
        """
        创建表SQL = """
        CREATE TABLE IF NOT EXISTS 交易对配置表 (
            序号 INTEGER PRIMARY KEY AUTOINCREMENT,
            交易所 TEXT NOT NULL,
            交易对 TEXT NOT NULL,
            基础货币 TEXT NOT NULL,
            报价货币 TEXT NOT NULL,
            最小下单量 REAL DEFAULT 0,
            最大下单量 REAL DEFAULT 999999,
            价格精度 INTEGER DEFAULT 8,
            数量精度 INTEGER DEFAULT 8,
            手续费率 REAL DEFAULT 0.001,
            是否启用 BOOLEAN DEFAULT 1,
            创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(交易所, 交易对)
        )
        """
        参_游标.execute(创建表SQL)
    
    def 清空表(self, 参_表名: str) -> bool:
        """
        清空指定的数据表
        
        参数:
            参_表名: 表名
            
        返回:
            操作是否成功
        """
        连接 = self.类_数据库连接管理.获取连接()
        游标 = 连接.cursor()
        
        # 获取实际表名（如果有表名映射）
        实际表名 = 参_表名
        if not 参_表名.endswith("表"):
            实际表名 = f"{参_表名}表"
        
        try:
            # 执行删除操作
            游标.execute(f"DELETE FROM {实际表名}")
            连接.commit()
            
            logger.info(f"表 {实际表名} 已清空")
            return True
            
        except Exception as e:
            连接.rollback()
            logger.error(f"清空表 {实际表名} 失败: {e}", exc_info=True)
            return False 