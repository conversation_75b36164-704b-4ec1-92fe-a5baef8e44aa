# -*- coding: utf-8 -*-
"""
UI初始化管理模块
负责主窗口的UI初始化和基础设置
"""

from PySide6.QtWidgets import QMainWindow, QApplication
from PySide6.QtCore import Qt
from 窗口UI布局.UI文件.Main_window_ui import Ui_MainWindow
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_UI初始化管理:
    """
    UI初始化管理类
    负责处理主窗口的UI初始化和基础设置
    """
    
    def __init__(self, 参_主窗口):
        """
        初始化UI管理器
        
        参数:
            参_主窗口: 主窗口实例
        """
        self.主窗口 = 参_主窗口
        logger.info("UI初始化管理类初始化完成")
    
    def 初始化界面(self):
        """
        初始化界面元素
        设置表格列名、默认值等
        """
        self.初始化策略控制表格()
        self.初始化持仓表格()
        self.初始化平仓表格()
        self.初始化历史订单表格()
        self.初始化日志表格()
        logger.info("界面初始化完成")
    
    def 初始化策略控制表格(self):
        """
        初始化策略控制表格
        """
        # 设置表格列名
        局_控制表格列名 = [
            "序号", "交易对", "预配本金", "预配首单/U", "本次下单/U", 
            "复利金额/U", "累计下单/U", "订单数量", "最大订单", "浮动收益/U", 
            "浮动收益率/%", "结算收益/U", "持仓均价/U", "持仓数量", "预估平仓价格", 
            "预估月化率", "预估年化率", "策略状态", "当前状态", "策略配置",
            "策略类型", "子策略", "创建时间", "机器人编号"
        ]
        self.主窗口.tableWidget_control.setColumnCount(len(局_控制表格列名))
        self.主窗口.tableWidget_control.setHorizontalHeaderLabels(局_控制表格列名)
        self.主窗口.tableWidget_control.verticalHeader().setVisible(False)
        self.主窗口.tableWidget_control.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 设置列宽
        列宽配置 = {
            0: 60,   # 序号
            1: 100,  # 交易对
            2: 100,  # 预配本金
            # ... 其他列宽配置
        }
        for 列, 宽度 in 列宽配置.items():
            self.主窗口.tableWidget_control.setColumnWidth(列, 宽度)
        
        logger.debug("策略控制表格初始化完成")
    
    def 初始化持仓表格(self):
        """
        初始化持仓表格
        """
        局_持仓表格列名 = [
            "序号", "交易对", "开仓蜡烛时间", "开仓时间", 
            "开仓金额", "开仓数量", "开仓手续费"
        ]
        self.主窗口.tableWidget_Open_Orders.setColumnCount(len(局_持仓表格列名))
        self.主窗口.tableWidget_Open_Orders.setHorizontalHeaderLabels(局_持仓表格列名)
        self.主窗口.tableWidget_Open_Orders.verticalHeader().setVisible(False)
        self.主窗口.tableWidget_Open_Orders.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        
        logger.debug("持仓表格初始化完成")
    
    def 初始化平仓表格(self):
        """
        初始化平仓表格
        """
        局_平仓表格列名 = [
            "序号", "交易对", "开仓蜡烛时间", "开仓时间", "开仓金额", 
            "开仓数量", "开仓手续费", "平仓蜡烛时间", "平仓时间", "平仓金额", 
            "平仓数量", "平仓收益", "平仓收益率", "平仓手续费"
        ]
        self.主窗口.tableWidget_Closing_Orders.setColumnCount(len(局_平仓表格列名))
        self.主窗口.tableWidget_Closing_Orders.setHorizontalHeaderLabels(局_平仓表格列名)
        self.主窗口.tableWidget_Closing_Orders.verticalHeader().setVisible(False)
        self.主窗口.tableWidget_Closing_Orders.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        
        logger.debug("平仓表格初始化完成")
    
    def 初始化历史订单表格(self):
        """
        初始化历史订单表格
        """
        局_历史表格列名 = [
            "序号", "交易对", "开仓蜡烛时间", "开仓时间", "开仓金额", 
            "开仓数量", "开仓手续费", "平仓蜡烛时间", "平仓时间", "平仓金额", 
            "平仓数量", "平仓收益", "平仓收益率", "平仓手续费", "策略配置",
            "策略类型", "子策略", "创建时间", "机器人编号"
        ]
        self.主窗口.tableWidget_Historical_orders.setColumnCount(len(局_历史表格列名))
        self.主窗口.tableWidget_Historical_orders.setHorizontalHeaderLabels(局_历史表格列名)
        self.主窗口.tableWidget_Historical_orders.verticalHeader().setVisible(False)
        self.主窗口.tableWidget_Historical_orders.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        
        logger.debug("历史订单表格初始化完成")
    
    def 初始化日志表格(self):
        """
        初始化日志表格
        """
        局_日志表格列名 = [
            "序号", "交易对", "机器人编号", "订单类型", "蜡烛时间", 
            "开平仓时间", "金额", "数量", "手续费", "收益/收益率", 
            "策略配置", "策略类型", "子策略"
        ]
        self.主窗口.tableWidget_OpenAndClose_log.setColumnCount(len(局_日志表格列名))
        self.主窗口.tableWidget_OpenAndClose_log.setHorizontalHeaderLabels(局_日志表格列名)
        self.主窗口.tableWidget_OpenAndClose_log.verticalHeader().setVisible(False)
        self.主窗口.tableWidget_OpenAndClose_log.horizontalHeader().setDefaultAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 设置日志表格列宽
        列宽配置 = {
            0: 60,   # 序号
            1: 100,  # 交易对
            2: 100,  # 机器人编号
            3: 80,   # 订单类型
            4: 120,  # 蜡烛时间
            5: 120,  # 开平仓时间
            6: 100,  # 金额
            7: 100,  # 数量
            8: 100,  # 手续费
            9: 120,  # 收益/收益率
            10: 100, # 策略配置
            11: 100, # 策略类型
            12: 100  # 子策略
        }
        for 列, 宽度 in 列宽配置.items():
            self.主窗口.tableWidget_OpenAndClose_log.setColumnWidth(列, 宽度)
        
        logger.debug("日志表格初始化完成")
    
    def 设置窗口属性(self):
        """
        设置窗口的基本属性
        """
        self.主窗口.setWindowTitle("现货量化软件 - 主控制台")
        self.居中显示窗口()
        logger.info("窗口属性设置完成")
    
    def 居中显示窗口(self):
        """
        让窗口在屏幕中央显示
        """
        局_屏幕 = QApplication.primaryScreen().geometry()
        局_窗口尺寸 = self.主窗口.geometry()
        
        局_x = (局_屏幕.width() - 局_窗口尺寸.width()) // 2
        局_y = (局_屏幕.height() - 局_窗口尺寸.height()) // 2
        
        self.主窗口.move(局_x, 局_y)
        logger.debug("窗口已居中显示") 