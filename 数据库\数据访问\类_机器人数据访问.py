"""
机器人数据访问类
负责机器人相关的数据操作
"""

import logging
import json
from typing import List, Dict, Any, Optional
from .类_数据库连接管理 import 类_数据库连接管理

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_机器人数据访问:
    """
    机器人数据访问类
    负责处理机器人相关的数据库操作
    """
    
    def __init__(self, 参_数据库连接管理: Optional[类_数据库连接管理] = None):
        """
        初始化机器人数据访问类
        
        参数:
            参_数据库连接管理: 数据库连接管理实例
        """
        self.类_数据库连接管理 = 参_数据库连接管理 or 类_数据库连接管理()
        self.类_模块名 = "机器人数据访问"
        logger.info("机器人数据访问类初始化完成")
        print("机器人数据访问类初始化完成")
    
    def 添加机器人(self, 参_机器人数据: Dict[str, Any]) -> bool:
        """
        添加机器人到中控表
        
        参数:
            参_机器人数据: 机器人数据字典
            
        返回:
            添加是否成功
        """
        try:
            连接 = self.类_数据库连接管理.获取连接()
            游标 = 连接.cursor()
            
            # 处理字段名映射和JSON转换
            if '策略参数' in 参_机器人数据:
                策略参数 = 参_机器人数据.pop('策略参数')
                if isinstance(策略参数, dict):
                    参_机器人数据['策略参数JSON'] = json.dumps(策略参数, ensure_ascii=False)
                else:
                    参_机器人数据['策略参数JSON'] = 策略参数
            
            # 构建插入SQL
            列名列表 = list(参_机器人数据.keys())
            占位符 = ', '.join(['?' for _ in 列名列表])
            列名字符串 = ', '.join(列名列表)
            
            SQL = f"INSERT INTO 中控表 ({列名字符串}) VALUES ({占位符})"
            游标.execute(SQL, list(参_机器人数据.values()))
            
            连接.commit()
            logger.info(f"添加机器人成功: {参_机器人数据.get('机器人编号', '未知')}")
            return True
            
        except Exception as e:
            logger.error(f"添加机器人失败: {e}", exc_info=True)
            print(f"添加机器人失败: {e}")
            return False
    
    def 获取所有机器人(self) -> List[Dict[str, Any]]:
        """
        获取所有机器人数据
        
        返回:
            机器人数据列表
        """
        try:
            连接 = self.类_数据库连接管理.获取连接()
            游标 = 连接.cursor()
            
            游标.execute("SELECT * FROM 中控表 ORDER BY 序号")
            结果列表 = 游标.fetchall()
            
            logger.debug(f"获取所有机器人数据成功，共 {len(结果列表)} 条记录")
            return [dict(行) for 行 in 结果列表]
            
        except Exception as e:
            logger.error(f"获取机器人数据失败: {e}", exc_info=True)
            print(f"获取机器人数据失败: {e}")
            return []
    
    def 获取机器人(self, 参_机器人编号: str) -> Optional[Dict[str, Any]]:
        """
        获取单个机器人数据
        
        参数:
            参_机器人编号: 机器人编号
            
        返回:
            机器人数据字典，如果未找到返回None
        """
        try:
            连接 = self.类_数据库连接管理.获取连接()
            游标 = 连接.cursor()
            
            游标.execute("SELECT * FROM 中控表 WHERE 机器人编号 = ?", (参_机器人编号,))
            结果 = 游标.fetchone()
            
            if 结果:
                logger.debug(f"获取机器人数据成功: {参_机器人编号}")
                return dict(结果)
            else:
                logger.warning(f"未找到机器人: {参_机器人编号}")
                print(f"未找到机器人: {参_机器人编号}")
                return None
                
        except Exception as e:
            logger.error(f"获取机器人数据失败: {参_机器人编号}, {e}", exc_info=True)
            print(f"获取机器人数据失败: {e}")
            return None
    
    def 更新机器人(self, 参_机器人编号: str, 参_更新数据: Dict[str, Any]) -> bool:
        """
        更新机器人数据
        
        参数:
            参_机器人编号: 机器人编号
            参_更新数据: 要更新的数据字典
            
        返回:
            更新是否成功
        """
        try:
            连接 = self.类_数据库连接管理.获取连接()
            游标 = 连接.cursor()
            
            # 构建更新SQL
            设置子句列表 = [f"{键} = ?" for 键 in 参_更新数据.keys()]
            设置子句 = ', '.join(设置子句列表)
            
            SQL = f"UPDATE 中控表 SET {设置子句} WHERE 机器人编号 = ?"
            参数列表 = list(参_更新数据.values()) + [参_机器人编号]
            
            游标.execute(SQL, 参数列表)
            连接.commit()
            
            if 游标.rowcount > 0:
                logger.info(f"机器人数据更新成功: {参_机器人编号}")
                print(f"机器人数据更新成功: {参_机器人编号}")
                return True
            else:
                logger.warning(f"机器人数据更新失败，未找到机器人: {参_机器人编号}")
                print(f"机器人数据更新失败，未找到机器人: {参_机器人编号}")
                return False
                
        except Exception as e:
            logger.error(f"更新机器人数据失败: {参_机器人编号}, {e}", exc_info=True)
            print(f"更新机器人数据失败: {e}")
            return False
    
    def 更新机器人状态(self, 参_机器人编号: str, 参_状态数据: Dict[str, Any]) -> bool:
        """
        更新机器人状态
        
        参数:
            参_机器人编号: 机器人编号
            参_状态数据: 要更新的状态数据
            
        返回:
            更新是否成功
        """
        try:
            连接 = self.类_数据库连接管理.获取连接()
            游标 = 连接.cursor()
            
            # 构建更新SQL
            设置子句列表 = [f"{键} = ?" for 键 in 参_状态数据.keys()]
            设置子句 = ', '.join(设置子句列表)
            
            SQL = f"UPDATE 中控表 SET {设置子句} WHERE 机器人编号 = ?"
            参数列表 = list(参_状态数据.values()) + [参_机器人编号]
            
            游标.execute(SQL, 参数列表)
            连接.commit()
            
            if 游标.rowcount > 0:
                logger.debug(f"机器人状态更新成功: {参_机器人编号}")
                return True
            else:
                logger.warning(f"机器人状态更新失败，未找到机器人: {参_机器人编号}")
                return False
            
        except Exception as e:
            logger.error(f"更新机器人状态失败: {参_机器人编号}, {e}", exc_info=True)
            print(f"更新机器人状态失败: {e}")
            return False 
            
    def 删除机器人(self, 参_机器人编号: str) -> bool:
        """
        从数据库中删除指定机器人编号的记录
        
        参数:
            参_机器人编号: 机器人编号
            
        返回:
            删除是否成功
        """
        try:
            连接 = self.类_数据库连接管理.获取连接()
            游标 = 连接.cursor()
            
            # 删除机器人记录
            SQL = "DELETE FROM 中控表 WHERE 机器人编号 = ?"
            游标.execute(SQL, (参_机器人编号,))
            连接.commit()
            
            if 游标.rowcount > 0:
                logger.info(f"机器人删除成功: {参_机器人编号}")
                print(f"机器人删除成功: {参_机器人编号}")
                return True
            else:
                logger.warning(f"机器人删除失败，未找到机器人: {参_机器人编号}")
                print(f"机器人删除失败，未找到机器人: {参_机器人编号}")
                return False
                
        except Exception as e:
            logger.error(f"删除机器人失败: {参_机器人编号}, {e}", exc_info=True)
            print(f"删除机器人失败: {e}")
            return False 