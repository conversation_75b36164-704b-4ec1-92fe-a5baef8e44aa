# -*- coding: utf-8 -*-
"""
简单OKX交易所类
只实现基本的蜡烛数据获取功能
适合新手学习使用
"""

import requests
import json
import time
from typing import List, Dict, Any, Optional
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)


class 类_OKX交易所:
    """
    简单OKX交易所类
    只实现蜡烛数据获取功能，去掉复杂的架构
    """
    
    def __init__(self):
        """
        初始化OKX交易所

        """
        self.类_基础URL = "https://www.okx.com"
        self.类_请求头 = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        print("OKX交易所初始化完成")
        logger.info("OKX交易所初始化完成")
    
    def 获取蜡烛数据(self, 参_交易对: str, 参_时间周期: str = "1m", 参_数量: int = 100) -> List[Dict[str, Any]]:
        """
        获取蜡烛数据（K线数据）
        
        参数:
            参_交易对: 交易对名称，如 "BTC-USDT"
            参_时间周期: 时间周期，如 "1m", "5m", "1H", "1D" 等
            参_数量: 返回K线数量，最大300
            
        返回:
            蜡烛数据列表，每个元素包含：
            {
                "时间戳": "1640995200000",
                "开盘价": "46200.1",
                "最高价": "46200.1", 
                "最低价": "46100.1",
                "收盘价": "46100.1",
                "成交量": "0.1",
                "成交额": "4610.01"
            }
        """
        try:
            # 记录开始执行
            print(f"开始获取蜡烛数据: {参_交易对}, 周期: {参_时间周期}, 数量: {参_数量}")
            logger.info(f"开始获取蜡烛数据: {参_交易对}, 周期: {参_时间周期}, 数量: {参_数量}")
            
            # 构建请求URL
            请求路径 = "/api/v5/market/candles"
            请求参数 = {
                "instId": 参_交易对,
                "bar": 参_时间周期,
                "limit": str(min(参_数量, 300))  # OKX最大限制300
            }
            
            # 发送GET请求
            完整URL = f"{self.类_基础URL}{请求路径}"
            响应 = requests.get(完整URL, params=请求参数, headers=self.类_请求头, timeout=10)
            
            # 检查响应状态
            if 响应.status_code != 200:
                print(f"请求失败，状态码: {响应.status_code}")
                logger.error(f"请求失败，状态码: {响应.status_code}")
                return []
            
            # 解析响应数据
            响应数据 = 响应.json()
            
            # 检查响应是否成功
            if 响应数据.get("code") != "0":
                错误信息 = 响应数据.get("msg", "未知错误")
                print(f"API返回错误: {错误信息}")
                logger.error(f"API返回错误: {错误信息}")
                return []
            
            # 解析蜡烛数据
            蜡烛数据列表 = []
            原始数据列表 = 响应数据.get("data", [])
            
            for 原始数据 in 原始数据列表:
                try:
                    解析后的数据 = self._解析蜡烛数据(原始数据)
                    蜡烛数据列表.append(解析后的数据)
                except Exception as e:
                    print(f"解析蜡烛数据失败: {e}")
                    logger.warning(f"解析蜡烛数据失败: {e}")
                    continue
            
            # 记录成功信息
            print(f"成功获取 {len(蜡烛数据列表)} 条蜡烛数据")
            logger.info(f"成功获取 {len(蜡烛数据列表)} 条蜡烛数据")
            
            return 蜡烛数据列表
            
        except requests.exceptions.Timeout:
            错误信息 = "请求超时"
            print(错误信息)
            logger.error(错误信息)
            return []
            
        except requests.exceptions.RequestException as e:
            错误信息 = f"网络请求失败: {e}"
            print(错误信息)
            logger.error(错误信息)
            return []
            
        except Exception as e:
            错误信息 = f"获取蜡烛数据失败: {e}"
            print(错误信息)
            logger.error(错误信息, exc_info=True)
            return []
    
    def 测试连接(self) -> bool:
        """
        测试与OKX服务器的连接
        
        返回:
            连接是否成功
        """
        try:
            print("开始测试连接...")
            
            # 使用获取服务器时间接口测试连接
            请求路径 = "/api/v5/public/time"
            完整URL = f"{self.类_基础URL}{请求路径}"
            
            响应 = requests.get(完整URL, headers=self.类_请求头, timeout=5)
            
            if 响应.status_code == 200:
                响应数据 = 响应.json()
                if 响应数据.get("code") == "0":
                    print("连接测试成功")
                    logger.info("连接测试成功")
                    return True
            
            print("连接测试失败")
            logger.warning("连接测试失败")
            return False
            
        except Exception as e:
            错误信息 = f"连接测试失败: {e}"
            print(错误信息)
            logger.error(错误信息)
            return False
    
    def _解析蜡烛数据(self, 参_原始数据: List[str]) -> Dict[str, str]:
        """
        解析OKX API返回的原始蜡烛数据
        
        参数:
            参_原始数据: OKX API返回的原始数据列表
                [时间戳, 开盘价, 最高价, 最低价, 收盘价, 成交量, 成交额, 成交笔数]
            
        返回:
            解析后的字典格式数据
        """
        try:
            if len(参_原始数据) < 8:
                raise ValueError("原始数据格式不正确")
            
            return {
                "时间戳": 参_原始数据[0],
                "开盘价": 参_原始数据[1],
                "最高价": 参_原始数据[2],
                "最低价": 参_原始数据[3],
                "收盘价": 参_原始数据[4],
                "成交量": 参_原始数据[5],
                "成交额": 参_原始数据[6],
                "成交笔数": 参_原始数据[7]
            }
            
        except Exception as e:
            print(f"解析蜡烛数据失败: {e}")
            logger.error(f"解析蜡烛数据失败: {e}")
            raise
   


# 使用示例
if __name__ == "__main__":
    # 创建交易所实例
    交易所 = 类_OKX交易所()
    
    # 测试连接
    if 交易所.测试连接():
        print("连接成功！")
        # 获取蜡烛数据
        蜡烛数据 = 交易所.获取蜡烛数据("BTC-USDT", "1m", 10)
        if 蜡烛数据:
            # 获取最新价格,用最新蜡烛的收盘价来确定最新价格
            最新价格 = 蜡烛数据[-1]['收盘价']
            print(f"BTC-USDT 最新价格: {最新价格}")
            print(f"获取到 {len(蜡烛数据)} 条蜡烛数据")
            for i, 数据 in enumerate(蜡烛数据[:3]):  # 只显示前3条
                print(f"第{i+1}条: 时间={数据['时间戳']}, 收盘价={数据['收盘价']}")
    else:
        print("连接失败！")   