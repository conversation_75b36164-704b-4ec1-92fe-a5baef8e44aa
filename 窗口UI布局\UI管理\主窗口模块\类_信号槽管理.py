# -*- coding: utf-8 -*-
"""
信号槽管理模块
负责主窗口的所有信号槽连接管理
"""

from PySide6.QtCore import Qt, QTimer
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_信号槽管理:
    """
    信号槽管理类
    负责处理主窗口的所有信号槽连接
    """
    
    def __init__(self, 参_主窗口):
        """
        初始化信号槽管理器
        
        参数:
            参_主窗口: 主窗口实例
        """
        self.主窗口 = 参_主窗口
        
        # 初始化定时器
        self.局_定时器 = QTimer()
        self.局_定时器.timeout.connect(self.主窗口.槽_定时更新数据)
        self.局_定时器.start(5000)  # 5000毫秒 = 5秒
        logger.info("信号槽管理类初始化完成")
    
    def 连接所有信号槽(self):
        """
        连接所有UI控件的信号槽
        这里定义了所有按钮点击、选择变化等事件的处理函数
        """
        self.连接全局控制按钮()
        self.连接交易所选择()
        self.连接下单模式选择()
        self.连接API配置按钮()
        self.连接表格事件()
        logger.info("所有信号槽连接完成")
    
    def 连接全局控制按钮(self):
        """
        连接全局控制按钮的信号槽
        """
        # 全部开始按钮
        self.主窗口.pushButton_All_Start.clicked.connect(self.主窗口.槽_全部开始)
        
        # 全部停止按钮
        self.主窗口.pushButton_All_Stop.clicked.connect(self.主窗口.槽_全部停止)
        
        # 全部暂停补单按钮
        self.主窗口.pushButton_All_Pause_Order.clicked.connect(self.主窗口.槽_全部暂停补单)
        
        # 全部恢复补单按钮
        self.主窗口.pushButton_All_Recovery_Order.clicked.connect(self.主窗口.槽_全部恢复补单)
        
        # 一键平仓按钮
        self.主窗口.pushButton_All_Sell.clicked.connect(self.主窗口.槽_一键平仓)
        
        # 清空全部按钮
        self.主窗口.pushButton_All_Clear.clicked.connect(self.主窗口.槽_清空全部)
        
        logger.debug("全局控制按钮信号槽连接完成")
    
    def 连接交易所选择(self):
        """
        连接交易所选择的信号槽
        """
        # 币安交易所选择
        self.主窗口.radioButton_Binance.toggled.connect(self.主窗口.槽_交易所变化)
        
        # OKX交易所选择
        self.主窗口.radioButton_OKX.toggled.connect(self.主窗口.槽_交易所变化)
        
        logger.debug("交易所选择信号槽连接完成")
    
    def 连接下单模式选择(self):
        """
        连接下单模式选择的信号槽
        """
        # 实盘下单模式
        self.主窗口.radioButton_Firm_Offer.toggled.connect(self.主窗口.槽_下单模式变化)
        
        # 线上模拟模式
        self.主窗口.radioButton_Online_Simulation.toggled.connect(self.主窗口.槽_下单模式变化)
        
        # 本地模拟模式
        self.主窗口.radioButton_Local_simulation.toggled.connect(self.主窗口.槽_下单模式变化)
        
        logger.debug("下单模式选择信号槽连接完成")
    
    def 连接API配置按钮(self):
        """
        连接API配置按钮的信号槽
        """
        # 币安API保存
        self.主窗口.pushButton_Binance_save.clicked.connect(self.主窗口.槽_保存币安API)
        
        # OKX API保存
        self.主窗口.pushButton_OKX_save.clicked.connect(self.主窗口.槽_保存OKX_API)
        
        logger.debug("API配置按钮信号槽连接完成")
    
    def 连接表格事件(self):
        """
        连接表格相关的信号槽
        """
        # 策略控制表格选择变化
        self.主窗口.tableWidget_control.itemSelectionChanged.connect(self.主窗口.槽_策略选择变化)
        
        # 策略控制表格双击事件
        self.主窗口.tableWidget_control.itemDoubleClicked.connect(self.主窗口.槽_编辑策略)
        
        # 策略控制表格右键菜单
        self.主窗口.tableWidget_control.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.主窗口.tableWidget_control.customContextMenuRequested.connect(self.主窗口.槽_显示表格右键菜单)
        
        logger.debug("表格事件信号槽连接完成") 