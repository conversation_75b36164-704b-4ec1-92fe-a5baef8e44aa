# -*- coding: utf-8 -*-
"""
表格数据管理模块
负责主窗口中所有表格的数据管理
"""

from PySide6.QtWidgets import QTableWidgetItem
from PySide6.QtCore import Qt
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_表格数据管理:
    """
    表格数据管理类
    负责处理主窗口中所有表格的数据管理
    """
    
    def __init__(self, 参_主窗口):
        """
        初始化表格数据管理器
        
        参数:
            参_主窗口: 主窗口实例
        """
        self.主窗口 = 参_主窗口
        self.类_模块名 = "表格数据管理"
        logger.info("表格数据管理类初始化完成")
    
    def 刷新中控表格数据(self):
        """
        刷新中控表格数据
        从数据库加载所有机器人数据并显示到表格中
        """
        if not self.主窗口.类_数据库管理:
            logger.warning("数据库管理不可用，无法刷新表格数据")
            print("数据库管理不可用，无法刷新表格数据")
            return
        
        # 获取所有机器人数据
        机器人列表 = self.主窗口.类_数据库管理.获取所有机器人()
        
        # 清空表格
        self.主窗口.tableWidget_control.setRowCount(0)
        
        # 添加数据到表格
        for 行索引, 机器人数据 in enumerate(机器人列表):
            self.主窗口.tableWidget_control.insertRow(行索引)
            
            # 按照表格列顺序填充数据
            列数据 = [
                str(机器人数据.get('序号', '')),
                str(机器人数据.get('交易对', '')),
                str(机器人数据.get('预配本金', 0)),
                str(机器人数据.get('预配首单', 0)),
                str(机器人数据.get('本次下单', 0)),
                str(机器人数据.get('复利金额', 0)),
                str(机器人数据.get('累计下单', 0)),
                str(机器人数据.get('订单数量', 0)),
                str(机器人数据.get('最大订单', 0)),
                str(机器人数据.get('浮动收益', 0)),
                str(机器人数据.get('浮动收益率', 0)),
                str(机器人数据.get('结算收益', 0)),
                str(机器人数据.get('持仓均价', 0)),
                str(机器人数据.get('持仓数量', 0)),
                str(机器人数据.get('预估平仓价格', 0)),
                str(机器人数据.get('预估月化率', 0)),
                str(机器人数据.get('预估年化率', 0)),
                str(机器人数据.get('策略状态', '')),
                str(机器人数据.get('当前状态', '')),
                str(机器人数据.get('策略配置', '')),
                str(机器人数据.get('策略类型', '')),
                str(机器人数据.get('子策略', '')),
                str(机器人数据.get('创建时间', '')),
                str(机器人数据.get('机器人编号', ''))
            ]
            
            for 列索引, 数据 in enumerate(列数据):
                self.主窗口.tableWidget_control.setItem(行索引, 列索引, self.创建居中表格项(数据))
        
        logger.debug(f"中控表格数据刷新完成，共加载 {len(机器人列表)} 个机器人")
        print(f"中控表格数据刷新完成，共加载 {len(机器人列表)} 个机器人")
    
    def 更新实时数据(self):
        """
        更新实时数据
        从交易所获取最新价格和账户信息，更新表格显示
        """
        if not self.主窗口.类_数据库管理:
            logger.warning("数据库管理不可用，无法更新实时数据")
            print("数据库管理不可用，无法更新实时数据")
            return
            
        try:
            # 获取所有机器人数据
            机器人列表 = self.主窗口.类_数据库管理.获取所有机器人()
            
            # 如果没有机器人数据，直接返回
            if not 机器人列表:
                logger.debug("没有机器人数据，无需更新实时数据")
                return
                
            # 遍历表格中的每一行，更新实时数据
            行数 = self.主窗口.tableWidget_control.rowCount()
            for 行索引 in range(行数):
                # 获取机器人编号
                机器人编号项 = self.主窗口.tableWidget_control.item(行索引, 23)  # 假设机器人编号在第24列
                if not 机器人编号项:
                    continue
                    
                机器人编号 = 机器人编号项.text()
                
                # 根据机器人编号查找对应的机器人数据
                机器人数据 = None
                for 数据 in 机器人列表:
                    if 数据.get('机器人编号') == 机器人编号:
                        机器人数据 = 数据
                        break
                
                if not 机器人数据:
                    logger.warning(f"未找到机器人数据: {机器人编号}")
                    continue
                
                # 更新表格中的实时数据（浮动收益、浮动收益率、持仓均价等）
                # 这里只更新几个关键字段，实际应用中可能需要更新更多字段
                self.主窗口.tableWidget_control.setItem(行索引, 9, self.创建居中表格项(str(机器人数据.get('浮动收益', 0))))  # 浮动收益
                self.主窗口.tableWidget_control.setItem(行索引, 10, self.创建居中表格项(str(机器人数据.get('浮动收益率', 0))))  # 浮动收益率
                self.主窗口.tableWidget_control.setItem(行索引, 12, self.创建居中表格项(str(机器人数据.get('持仓均价', 0))))  # 持仓均价
                self.主窗口.tableWidget_control.setItem(行索引, 13, self.创建居中表格项(str(机器人数据.get('持仓数量', 0))))  # 持仓数量
                self.主窗口.tableWidget_control.setItem(行索引, 17, self.创建居中表格项(str(机器人数据.get('策略状态', ''))))  # 策略状态
                self.主窗口.tableWidget_control.setItem(行索引, 18, self.创建居中表格项(str(机器人数据.get('当前状态', ''))))  # 当前状态
            
            logger.debug("实时数据更新完成")
            # print("实时数据更新完成")
            
        except Exception as e:
            logger.error(f"更新实时数据时发生错误: {e}", exc_info=True)
            print(f"更新实时数据时发生错误: {e}")
            return
    
    def 创建居中表格项(self, 参_文本: str) -> QTableWidgetItem:
        """
        创建一个居中对齐的表格项
        
        参数:
            参_文本: 表格项显示的文本
            
        返回:
            居中对齐的QTableWidgetItem对象
        """
        项目 = QTableWidgetItem(str(参_文本))
        项目.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        return 项目 