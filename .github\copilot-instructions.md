# Python项目开发规范

## 一、基本规范

1. **语言使用**：所有回复和代码使用中文，包括变量、函数、注释
2. **系统函数**：Python内置函数不要使用中文，以免造成程序出错
3. **称呼规范**：对我的称呼用"靓仔"

## 二、项目结构规范（模块化设计）

### 2.1 模块化设计原则

1. **单一职责**：每个模块只负责一个功能
2. **易扩展**：新增功能时不影响现有模块
3. **模块管理**：用包的方式来管理模块
4. **基类优先**：有基类时优先使用基类接口，保持接口统一

## 三、命名规范

### 3.1 核心命名规则

| 变量类型     | 前缀  | 示例            | 说明                     |
|-------------|-------|----------------|--------------------------|
| **全局变量** | `全_` | `全_数据库连接` | 整个程序都能访问的变量    |
| **类的变量** | `类_` | `类_用户计数`   | 类中定义的变量           |
| **参数**     | `参_` | `参_用户名`     | 函数的输入参数           |
| **局部变量** | 无前缀 | `用户名`       | 函数内部使用的变量       |

### 3.2 文件和类命名

```python
# 文件命名
类_主窗口.py              # 类文件以"类_"开头
数据库管理.py             # 功能文件直接用功能名

# 类命名
class 类_主窗口:          # 类名以"类_"开头
class 类_数据库管理:      # 清晰表达类的功能
```

### 3.3 数据类型命名

```python
# 基本数据类型
列表_用户数据 = []        # 列表类型
字典_配置信息 = {}        # 字典类型
集合_唯一值 = set()       # 集合类型
元组_坐标点 = ()          # 元组类型
```

## 四、模块设计规范

### 4.1 类的设计规范

```python
class 类_示例模块:
    """
    模块功能的详细描述
    负责处理xxx相关的业务逻辑
    """
    
    def __init__(self, 参_初始化参数):
        """
        初始化方法
        
        参数:
            参_初始化参数: 初始化参数说明
        """
        self.类_属性名 = 参_初始化参数
        self.类_状态 = "初始化"
        
        # 初始化日志
        print(f"{self.__class__.__name__} 初始化完成")
    
    def 公共方法(self, 参_输入参数):
        """
        公共方法，供外部调用
        
        参数:
            参_输入参数: 输入参数说明
            
        返回:
            返回值说明
        """
        # 方法实现
        pass
    
    def _私有方法(self, 参_内部参数):
        """
        私有方法，仅供内部使用
        方法名以下划线开头
        """
        # 内部实现
        pass
```


## 五、错误处理规范

### 5.1 异常处理

```python
def 安全执行方法(self, 参_操作参数):
    """
    带异常处理的方法示例
    结合日志记录的异常处理规范
    """
    try:
        # 记录开始执行的调试信息
        logging.debug(f"[{self.__class__.__name__}] 开始执行业务逻辑，参数：{参_操作参数}")
        
        # 主要业务逻辑
        结果 = self.执行业务逻辑(参_操作参数)
        
        # 记录成功信息
        logging.info(f"[{self.__class__.__name__}] 业务逻辑执行成功，结果：{结果}")
        return 结果
        
    except ValueError as e:
        # 记录参数错误，使用warning级别
        logging.warning(f"[{self.__class__.__name__}] 参数错误: {e}")
        return None
        
    except Exception as e:
        # 记录未知错误，使用error级别，并包含完整堆栈信息
        logging.error(f"[{self.__class__.__name__}] 执行过程中发生未知错误", exc_info=True)
        return None
```

### 5.2 严重错误处理

```python
def 关键业务方法(self, 参_操作参数):
    """
    处理关键业务逻辑的方法示例
    当发生严重错误时使用critical级别记录
    """
    try:
        # 记录开始执行的调试信息
        logging.debug(f"[{self.__class__.__name__}] 开始执行关键业务逻辑，参数：{参_操作参数}")
        
        # 检查系统状态
        if not self._检查系统状态():
            logging.critical(f"[{self.__class__.__name__}] 系统状态异常，无法执行关键业务", exc_info=True)
            return None
            
        # 执行关键业务逻辑
        结果 = self._执行关键业务(参_操作参数)
        
        # 记录成功信息
        logging.info(f"[{self.__class__.__name__}] 关键业务执行成功")
        return 结果
        
    except Exception as e:
        # 记录严重错误，使用critical级别
        logging.critical(f"[{self.__class__.__name__}] 执行关键业务过程中发生严重错误", exc_info=True)
        # 可以在这里添加其他错误处理逻辑，如：发送告警、系统退出等
        return None
```


## 六、代码组织规范

### 6.1 类内方法组织顺序

```python
class 类_示例:
    """类的说明"""
    
    # 1. 类变量
    类_版本 = "1.0"
    
    # 2. 初始化方法
    def __init__(self):
        pass
    
    # 3. 公共方法（按功能分组）
    def 获取数据(self):
        pass
    
    def 保存数据(self):
        pass
    
    # 4. 事件处理方法
    def 槽_按钮点击(self):
        pass
    
    # 5. 私有方法
    def _内部处理(self):
        pass
    
    # 6. 静态方法和类方法
    @staticmethod
    def 工具方法():
        pass
```

## 七、模块间通信规范

### 7.1 信号槽机制

```python
from PySide6.QtCore import Signal, QObject

class 类_事件管理(QObject):
    """
    事件管理类，负责模块间通信
    """
    # 定义信号
    信号_数据更新 = Signal(str, dict)
    信号_状态变化 = Signal(str)
    
    def 发送数据更新信号(self, 参_数据类型, 参_数据):
        """发送数据更新信号"""
        self.信号_数据更新.emit(参_数据类型, 参_数据)
```

## 八、日志记录规范

### 8.1 日志级别使用

```python
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_机器人数据访问:
    def __init__(self):
        logger.info("机器人数据访问类初始化完成")

    def 保存配置(self):
        logger.info("保存配置成功")
```

