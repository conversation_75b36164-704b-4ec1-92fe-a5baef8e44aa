# -*- coding: utf-8 -*-
"""
主窗口逻辑类
负责处理主窗口的所有业务逻辑和用户交互
"""

import sys
import os
import logging
from PySide6.QtWidgets import QMainWindow, QApplication, QMessageBox, QMenu
from PySide6.QtCore import Signal, Qt
from 窗口UI布局.UI文件.Main_window_ui import Ui_MainWindow

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

# 导入新的模块管理类
from 窗口UI布局.UI管理.主窗口模块 import (
    类_UI初始化管理,
    类_信号槽管理,
    类_表格数据管理,
    类_API配置管理,
    类_策略控制管理,
    类_状态栏管理
)

# 数据库模块导入
try:
    # 添加项目根目录到sys.path
    当前目录 = os.path.dirname(os.path.abspath(__file__))
    项目根目录 = os.path.dirname(os.path.dirname(当前目录))
    if 项目根目录 not in sys.path:
        sys.path.insert(0, 项目根目录)
    
    from 数据库.数据库管理 import 类_数据库管理
    logger.info("数据库模块导入成功")
except ImportError as e:
    logger.error(f"无法导入数据库管理模块，数据库功能将不可用", exc_info=True)
    类_数据库管理 = None


class 类_主窗口(QMainWindow, Ui_MainWindow):
    """
    主窗口类
    继承自QMainWindow和Ui_MainWindow
    负责处理主窗口的所有用户交互和业务逻辑
    """
    
    # 定义信号，用于与其他模块通信
    信号_策略状态变化 = Signal(str, str)  # 策略名称, 新状态
    信号_全局操作 = Signal(str)  # 操作类型
    
    def __init__(self):
        """
        初始化主窗口
        """
        super().__init__()
        
        # 设置UI界面
        self.setupUi(self)
        
        # 初始化变量
        self.局_策略列表 = []  # 存储所有策略信息
        self.局_当前选中策略 = None  # 当前选中的策略
        self.类_模块名 = "主窗口"
        
        logger.info("开始初始化主窗口")
        
        # 初始化数据库管理
        if 类_数据库管理:
            self.类_数据库管理 = 类_数据库管理()
            logger.info("数据库管理初始化成功")
        else:
            self.类_数据库管理 = None
            logger.warning("数据库管理初始化失败，将使用默认配置")
        
        # 初始化状态栏管理
        self.类_状态栏管理 = 类_状态栏管理(self.statusBar())
        
        # 初始化各个功能模块
        self.UI初始化管理 = 类_UI初始化管理(self)
        self.信号槽管理 = 类_信号槽管理(self)
        self.表格数据管理 = 类_表格数据管理(self)
        self.API配置管理 = 类_API配置管理(self)
        self.策略控制管理 = 类_策略控制管理(self)
        
        # 初始化界面
        self.UI初始化管理.初始化界面()
        self.UI初始化管理.设置窗口属性()
        
        # 连接信号槽
        self.信号槽管理.连接所有信号槽()
        
        # 加载保存的配置
        self.加载保存的配置()
        
        # 显示初始化完成消息
        self.类_状态栏管理.显示系统提示("系统初始化完成")
        logger.info("主窗口初始化完成")
        
        # print("主窗口初始化完成")
    
    def 显示状态栏消息(self, 参_消息: str, 参_超时毫秒: int = 5000):
        """
        在状态栏显示系统提示消息
        
        参数:
            参_消息: 要显示的消息
            参_超时毫秒: 消息显示时间，默认5秒
        """
        self.类_状态栏管理.显示系统提示(参_消息, 参_超时毫秒)
    
    def 加载保存的配置(self):
        """
        加载之前保存的配置
        """
        logger.info("加载保存的配置")
        
        if not self.类_数据库管理:
            logger.warning("数据库管理不可用，跳过配置加载")
            return
        
        # 加载交易所选择
        当前交易所 = self.类_数据库管理.获取系统配置("当前交易所")
        if 当前交易所 == "币安":
            self.radioButton_Binance.setChecked(True)
        elif 当前交易所 == "OKX":
            self.radioButton_OKX.setChecked(True)
        
        # 加载下单模式
        下单模式 = self.类_数据库管理.获取系统配置("下单模式")
        if 下单模式 == "实盘下单":
            self.radioButton_Firm_Offer.setChecked(True)
        elif 下单模式 == "线上模拟":
            self.radioButton_Online_Simulation.setChecked(True)
        elif 下单模式 == "本地模拟":
            self.radioButton_Local_simulation.setChecked(True)
        
        # 加载API配置
        self.API配置管理.加载API配置()
        
        # 加载机器人数据到中控表格
        self.表格数据管理.刷新中控表格数据()
        
        logger.info("配置加载完成")
    
    # ========== 全局控制按钮槽函数 ==========
    
    def 槽_全部开始(self):
        """
        全部开始按钮点击事件
        启动所有策略
        """
        确认结果 = QMessageBox.question(
            self,
            "确认操作",
            "确定要启动所有策略吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if 确认结果 == QMessageBox.StandardButton.Yes:
            self.策略控制管理.启动所有策略()
            self.类_状态栏管理.显示系统提示("所有策略已启动")
            self.信号_全局操作.emit("启动所有")
            logger.info("启动所有策略")
        else:
            logger.info("取消启动所有策略")
    
    def 槽_全部停止(self):
        """
        全部停止按钮点击事件
        停止所有策略
        """
        确认结果 = QMessageBox.question(
            self,
            "确认操作",
            "确定要停止所有策略吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if 确认结果 == QMessageBox.StandardButton.Yes:
            self.策略控制管理.停止所有策略()
            self.类_状态栏管理.显示系统提示("所有策略已停止")
            self.信号_全局操作.emit("停止所有")
            logger.info("停止所有策略")
        else:
            logger.info("取消停止所有策略")
    
    def 槽_全部暂停补单(self):
        """
        全部暂停补单按钮点击事件
        暂停所有策略的补单功能
        """
        self.策略控制管理.暂停所有策略补单()
        self.类_状态栏管理.显示系统提示("所有策略已暂停补单")
        self.信号_全局操作.emit("暂停所有补单")
        logger.info("暂停所有策略补单")
    
    def 槽_全部恢复补单(self):
        """
        全部恢复补单按钮点击事件
        恢复所有策略的补单功能
        """
        self.策略控制管理.恢复所有策略补单()
        self.类_状态栏管理.显示系统提示("所有策略已恢复补单")
        self.信号_全局操作.emit("恢复所有补单")
        logger.info("恢复所有策略补单")
    
    def 槽_一键平仓(self):
        """
        一键平仓按钮点击事件
        平掉所有持仓
        """
        logger.info("点击了一键平仓按钮")
        
        # 危险操作，需要二次确认
        局_回复 = QMessageBox.warning(
            self, "危险操作", 
            "⚠️ 一键平仓将关闭所有持仓！\n此操作不可撤销，确定继续吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if 局_回复 == QMessageBox.StandardButton.Yes:
            # 再次确认
            局_二次确认 = QMessageBox.critical(
                self, "最终确认", 
                "🚨 最后确认：真的要平掉所有仓位吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if 局_二次确认 == QMessageBox.StandardButton.Yes:
                self.策略控制管理.执行一键平仓()
                self.显示状态栏消息("一键平仓指令已发送！")
    
    def 槽_清空全部(self):
        """
        清空全部按钮点击事件
        清空数据库中的中控表、平仓表、持仓表、日志表的所有数据
        """
        logger.info("点击了清空全部按钮")
        
        # 危险操作，需要二次确认
        局_回复 = QMessageBox.warning(
            self, "危险操作", 
            "⚠️ 清空全部将删除所有数据库记录！\n包括中控表、平仓表、持仓表和日志表的所有数据。\n此操作不可撤销，确定继续吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if 局_回复 == QMessageBox.StandardButton.Yes:
            # 再次确认
            局_二次确认 = QMessageBox.critical(
                self, "最终确认", 
                "🚨 最后确认：真的要清空所有数据吗？\n此操作将永久删除所有交易记录和策略数据！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if 局_二次确认 == QMessageBox.StandardButton.Yes:
                self.执行清空全部数据()
                self.显示状态栏消息("所有数据已清空！")
    
    # ========== 交易所和模式选择槽函数 ==========
    
    def 槽_交易所变化(self):
        """
        交易所选择变化事件
        """
        if self.radioButton_Binance.isChecked():
            局_选中交易所 = "币安"
        elif self.radioButton_OKX.isChecked():
            局_选中交易所 = "OKX"
        else:
            局_选中交易所 = "未知"
        
        logger.info(f"交易所切换到: {局_选中交易所}")
        
        # 保存交易所选择到数据库
        if self.类_数据库管理:
            self.类_数据库管理.保存系统配置("当前交易所", 局_选中交易所)
    
    def 槽_下单模式变化(self):
        """
        下单模式选择变化事件
        """
        if self.radioButton_Firm_Offer.isChecked():
            局_选中模式 = "实盘下单"
        elif self.radioButton_Online_Simulation.isChecked():
            局_选中模式 = "线上模拟"
        elif self.radioButton_Local_simulation.isChecked():
            局_选中模式 = "本地模拟"
        else:
            局_选中模式 = "未知"
        
        logger.info(f"下单模式切换到: {局_选中模式}")
        
        # 保存下单模式选择到数据库
        if self.类_数据库管理:
            self.类_数据库管理.保存系统配置("下单模式", 局_选中模式)
    
    # ========== API配置槽函数 ==========
    
    def 槽_保存币安API(self):
        """
        保存币安API配置
        """
        self.API配置管理.保存币安API()
    
    def 槽_保存OKX_API(self):
        """
        保存OKX API配置
        """
        self.API配置管理.保存OKX_API()
    
    # ========== 表格操作槽函数 ==========
    
    def 槽_策略选择变化(self):
        """
        策略表格选择变化事件
        """
        局_当前行 = self.tableWidget_control.currentRow()
        if 局_当前行 >= 0:
            # 获取选中的策略名称
            局_策略名称项 = self.tableWidget_control.item(局_当前行, 0)
            if 局_策略名称项:
                self.局_当前选中策略 = 局_策略名称项.text()
                logger.info(f"选中策略: {self.局_当前选中策略}")
    
    def 槽_编辑策略(self, 参_项目):
        """
        双击策略表格项编辑策略
        """
        局_行号 = 参_项目.row()
        self.策略控制管理.修改机器人(局_行号)
    
    def 槽_显示表格右键菜单(self, 参_位置):
        """
        显示表格右键菜单
        
        参数:
            参_位置: 鼠标点击的位置
        """
        logger.info("显示表格右键菜单")
        
        # 创建菜单
        菜单 = QMenu(self)
        
        # 获取当前选中行
        当前行 = self.tableWidget_control.currentRow()
        
        # 如果有选中行，则显示完整菜单
        if 当前行 >= 0 and self.tableWidget_control.item(当前行, 0) is not None:
            # 添加菜单项
            动作_启动 = 菜单.addAction("启动")
            动作_停止 = 菜单.addAction("停止")
            动作_暂停补单 = 菜单.addAction("暂停补单")
            动作_恢复补单 = 菜单.addAction("恢复补单")
            动作_平仓 = 菜单.addAction("平仓")
            动作_删除 = 菜单.addAction("删除")
            
            # 添加分割线
            菜单.addSeparator()
            
            # 添加新建和修改菜单项
            动作_新建机器人 = 菜单.addAction("新建机器人")
            动作_修改机器人 = 菜单.addAction("修改机器人")
            
            # 连接菜单项的信号槽
            动作_启动.triggered.connect(lambda: self.策略控制管理.单个启动(当前行))
            动作_停止.triggered.connect(lambda: self.策略控制管理.单个停止(当前行))
            动作_暂停补单.triggered.connect(lambda: self.策略控制管理.单个暂停补单(当前行))
            动作_恢复补单.triggered.connect(lambda: self.策略控制管理.单个恢复补单(当前行))
            动作_平仓.triggered.connect(lambda: self.策略控制管理.单个平仓(当前行))
            动作_删除.triggered.connect(lambda: self.策略控制管理.单个删除(当前行))
            动作_新建机器人.triggered.connect(self.策略控制管理.新建机器人)
            动作_修改机器人.triggered.connect(lambda: self.策略控制管理.修改机器人(当前行))
        else:
            # 如果没有选中行，只显示新建菜单项
            动作_新建机器人 = 菜单.addAction("新建机器人")
            动作_新建机器人.triggered.connect(self.策略控制管理.新建机器人)
        
        # 在鼠标位置显示菜单
        菜单.exec(self.tableWidget_control.viewport().mapToGlobal(参_位置))
    
    def 槽_定时更新数据(self):
        """
        定时更新数据
        每5秒执行一次，更新价格、盈亏等实时数据
        """
        self.表格数据管理.更新实时数据()
        self.类_状态栏管理.显示系统提示("系统正常运行中", 0)  # 0表示不自动消失
    
    def 执行清空全部数据(self):
        """
        执行清空全部数据操作
        清空数据库中的所有表数据
        """
        if self.类_数据库管理:
            try:
                self.类_数据库管理.清空所有表()
                self.表格数据管理.刷新中控表格数据()
                logger.info("所有数据已清空")
            except Exception as e:
                logger.error("清空数据失败", exc_info=True)
                print(f"清空数据失败: {e}")
        else:
            logger.warning("数据库不可用，无法清空数据")
            print("数据库不可用，无法清空数据")


# 测试代码
if __name__ == "__main__":
    """
    测试主窗口类
    """
    局_应用 = QApplication(sys.argv)
    
    # 创建主窗口
    局_主窗口 = 类_主窗口()
    
    # 显示窗口
    局_主窗口.show()
    
    # 运行应用
    sys.exit(局_应用.exec()) 