2025-07-13 14:36:06,030 - root - INFO - 日志系统初始化完成
2025-07-13 14:36:06,301 - 窗口UI布局.UI管理.类_主窗口 - INFO - 数据库模块导入成功
2025-07-13 14:36:06,301 - root - INFO - 开始初始化应用程序
2025-07-13 14:36:06,438 - 窗口UI布局.UI管理.类_主窗口 - INFO - 开始初始化主窗口
2025-07-13 14:36:06,452 - 数据库.数据访问.类_表结构管理 - INFO - 数据库表创建完成
2025-07-13 14:36:06,452 - 数据库.数据访问.类_表结构管理 - INFO - 表结构管理类初始化完成
2025-07-13 14:36:06,453 - 数据库.数据访问.类_机器人数据访问 - INFO - 机器人数据访问类初始化完成
2025-07-13 14:36:06,453 - 数据库.数据访问.类_配置数据访问 - INFO - 配置数据访问类初始化完成
2025-07-13 14:36:06,453 - 数据库.数据访问.类_交易对数据访问 - INFO - 交易对数据访问类初始化完成
2025-07-13 14:36:06,454 - 数据库.数据访问.类_开平仓日志数据访问 - INFO - 开平仓日志数据访问类初始化完成
2025-07-13 14:36:06,454 - 数据库.数据访问.类_交易对数据访问 - INFO - 交易对配置表已有数据，跳过初始化
2025-07-13 14:36:06,455 - 数据库.数据库管理 - INFO - 数据库管理类初始化完成，数据库路径: d:\python-project\Quantification\数据库\量化交易数据.db
2025-07-13 14:36:06,455 - 窗口UI布局.UI管理.类_主窗口 - INFO - 数据库管理初始化成功
2025-07-13 14:36:06,456 - 窗口UI布局.UI管理.主窗口模块.类_状态栏管理 - INFO - 状态栏管理类初始化完成
2025-07-13 14:36:06,456 - 窗口UI布局.UI管理.主窗口模块.类_UI初始化管理 - INFO - UI初始化管理类初始化完成
2025-07-13 14:36:06,457 - 窗口UI布局.UI管理.主窗口模块.类_信号槽管理 - INFO - 信号槽管理类初始化完成
2025-07-13 14:36:06,457 - 窗口UI布局.UI管理.主窗口模块.类_表格数据管理 - INFO - 表格数据管理类初始化完成
2025-07-13 14:36:06,457 - 模块类.功能类.类_加密工具 - INFO - 加密工具类初始化完成
2025-07-13 14:36:06,457 - 窗口UI布局.UI管理.主窗口模块.类_API配置管理 - INFO - API配置管理类初始化完成
2025-07-13 14:36:06,458 - 窗口UI布局.UI管理.主窗口模块.类_策略控制管理 - INFO - 策略控制管理类初始化完成
2025-07-13 14:36:06,463 - 窗口UI布局.UI管理.主窗口模块.类_UI初始化管理 - INFO - 界面初始化完成
2025-07-13 14:36:06,466 - 窗口UI布局.UI管理.主窗口模块.类_UI初始化管理 - INFO - 窗口属性设置完成
2025-07-13 14:36:06,467 - 窗口UI布局.UI管理.主窗口模块.类_信号槽管理 - INFO - 所有信号槽连接完成
2025-07-13 14:36:06,467 - 窗口UI布局.UI管理.类_主窗口 - INFO - 加载保存的配置
2025-07-13 14:36:06,467 - 数据库.数据访问.类_配置数据访问 - INFO - 获取系统配置成功: 当前交易所
2025-07-13 14:36:06,467 - 数据库.数据访问.类_配置数据访问 - INFO - 获取系统配置成功: 下单模式
2025-07-13 14:36:06,468 - 数据库.数据访问.类_配置数据访问 - INFO - 获取API配置成功: 币安
2025-07-13 14:36:06,473 - 数据库.数据访问.类_配置数据访问 - INFO - 获取API配置成功: OKX
2025-07-13 14:36:06,473 - 窗口UI布局.UI管理.主窗口模块.类_API配置管理 - INFO - API配置加载完成
2025-07-13 14:36:06,474 - 窗口UI布局.UI管理.类_主窗口 - INFO - 配置加载完成
2025-07-13 14:36:06,475 - 窗口UI布局.UI管理.类_主窗口 - INFO - 主窗口初始化完成
2025-07-13 14:36:12,190 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 3
2025-07-13 14:36:14,493 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 2
2025-07-13 14:36:14,834 - 窗口UI布局.UI管理.类_主窗口 - INFO - 显示表格右键菜单
2025-07-13 14:36:15,584 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 3
2025-07-13 14:36:19,723 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 2
2025-07-13 14:36:20,262 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 1
2025-07-13 14:36:20,704 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 3
2025-07-13 14:36:21,094 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 3
2025-07-13 14:36:27,022 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 2
2025-07-13 14:36:27,084 - 窗口UI布局.UI管理.类_主窗口 - INFO - 显示表格右键菜单
2025-07-13 14:36:28,550 - 窗口UI布局.UI管理.主窗口模块.类_策略控制管理 - INFO - 开始新建机器人
2025-07-13 14:36:28,555 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 创建默认策略参数服务成功
2025-07-13 14:36:28,563 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 已加载 3 个 OKX 交易对
2025-07-13 14:36:28,564 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 策略配置窗口初始化完成
2025-07-13 14:36:33,628 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 新建策略确定
2025-07-13 14:36:33,635 - 数据库.数据访问.类_机器人数据访问 - INFO - 添加机器人成功: BOT_1752388593_8589
2025-07-13 14:36:33,635 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 机器人数据保存成功: BOT_1752388593_8589
2025-07-13 14:36:33,645 - 窗口UI布局.UI管理.主窗口模块.类_策略控制管理 - INFO - 策略配置已保存
2025-07-13 14:36:34,548 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 4
2025-07-13 14:36:36,209 - 窗口UI布局.UI管理.类_主窗口 - INFO - 点击了清空全部按钮
2025-07-13 14:36:38,104 - 数据库.数据访问.类_表结构管理 - INFO - 表 中控表 已清空
2025-07-13 14:36:38,104 - 数据库.数据访问.类_表结构管理 - ERROR - 清空表 开平仓日志表 失败: no such table: 开平仓日志表
Traceback (most recent call last):
  File "d:\python-project\Quantification\数据库\数据访问\类_表结构管理.py", line 332, in 清空表
    游标.execute(f"DELETE FROM {实际表名}")
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such table: 开平仓日志表
2025-07-13 14:36:38,113 - 数据库.数据访问.类_表结构管理 - INFO - 表 持仓表 已清空
2025-07-13 14:36:38,113 - 数据库.数据库管理 - INFO - 所有数据表已清空
2025-07-13 14:36:38,114 - 窗口UI布局.UI管理.类_主窗口 - INFO - 所有数据已清空
2025-07-13 14:39:03,282 - root - INFO - 日志系统初始化完成
2025-07-13 14:39:03,384 - 窗口UI布局.UI管理.类_主窗口 - INFO - 数据库模块导入成功
2025-07-13 14:39:03,385 - root - INFO - 开始初始化应用程序
2025-07-13 14:39:03,432 - 窗口UI布局.UI管理.类_主窗口 - INFO - 开始初始化主窗口
2025-07-13 14:39:03,438 - 数据库.数据访问.类_表结构管理 - INFO - 数据库表创建完成
2025-07-13 14:39:03,438 - 数据库.数据访问.类_表结构管理 - INFO - 表结构管理类初始化完成
2025-07-13 14:39:03,438 - 数据库.数据访问.类_机器人数据访问 - INFO - 机器人数据访问类初始化完成
2025-07-13 14:39:03,438 - 数据库.数据访问.类_配置数据访问 - INFO - 配置数据访问类初始化完成
2025-07-13 14:39:03,438 - 数据库.数据访问.类_交易对数据访问 - INFO - 交易对数据访问类初始化完成
2025-07-13 14:39:03,438 - 数据库.数据访问.类_开平仓日志数据访问 - INFO - 开平仓日志数据访问类初始化完成
2025-07-13 14:39:03,438 - 数据库.数据访问.类_交易对数据访问 - INFO - 交易对配置表已有数据，跳过初始化
2025-07-13 14:39:03,439 - 数据库.数据库管理 - INFO - 数据库管理类初始化完成，数据库路径: d:\python-project\Quantification\数据库\量化交易数据.db
2025-07-13 14:39:03,439 - 窗口UI布局.UI管理.类_主窗口 - INFO - 数据库管理初始化成功
2025-07-13 14:39:03,439 - 窗口UI布局.UI管理.主窗口模块.类_状态栏管理 - INFO - 状态栏管理类初始化完成
2025-07-13 14:39:03,439 - 窗口UI布局.UI管理.主窗口模块.类_UI初始化管理 - INFO - UI初始化管理类初始化完成
2025-07-13 14:39:03,439 - 窗口UI布局.UI管理.主窗口模块.类_信号槽管理 - INFO - 信号槽管理类初始化完成
2025-07-13 14:39:03,440 - 窗口UI布局.UI管理.主窗口模块.类_表格数据管理 - INFO - 表格数据管理类初始化完成
2025-07-13 14:39:03,440 - 模块类.功能类.类_加密工具 - INFO - 加密工具类初始化完成
2025-07-13 14:39:03,440 - 窗口UI布局.UI管理.主窗口模块.类_API配置管理 - INFO - API配置管理类初始化完成
2025-07-13 14:39:03,440 - 窗口UI布局.UI管理.主窗口模块.类_策略控制管理 - INFO - 策略控制管理类初始化完成
2025-07-13 14:39:03,442 - 窗口UI布局.UI管理.主窗口模块.类_UI初始化管理 - INFO - 界面初始化完成
2025-07-13 14:39:03,442 - 窗口UI布局.UI管理.主窗口模块.类_UI初始化管理 - INFO - 窗口属性设置完成
2025-07-13 14:39:03,442 - 窗口UI布局.UI管理.主窗口模块.类_信号槽管理 - INFO - 所有信号槽连接完成
2025-07-13 14:39:03,442 - 窗口UI布局.UI管理.类_主窗口 - INFO - 加载保存的配置
2025-07-13 14:39:03,442 - 数据库.数据访问.类_配置数据访问 - INFO - 获取系统配置成功: 当前交易所
2025-07-13 14:39:03,442 - 数据库.数据访问.类_配置数据访问 - INFO - 获取系统配置成功: 下单模式
2025-07-13 14:39:03,443 - 数据库.数据访问.类_配置数据访问 - INFO - 获取API配置成功: 币安
2025-07-13 14:39:03,444 - 数据库.数据访问.类_配置数据访问 - INFO - 获取API配置成功: OKX
2025-07-13 14:39:03,444 - 窗口UI布局.UI管理.主窗口模块.类_API配置管理 - INFO - API配置加载完成
2025-07-13 14:39:03,444 - 窗口UI布局.UI管理.类_主窗口 - INFO - 配置加载完成
2025-07-13 14:39:03,444 - 窗口UI布局.UI管理.类_主窗口 - INFO - 主窗口初始化完成
2025-07-13 14:39:13,585 - 窗口UI布局.UI管理.类_主窗口 - INFO - 显示表格右键菜单
2025-07-13 14:39:17,059 - 窗口UI布局.UI管理.类_主窗口 - INFO - 显示表格右键菜单
2025-07-13 14:39:18,023 - 窗口UI布局.UI管理.主窗口模块.类_策略控制管理 - INFO - 开始新建机器人
2025-07-13 14:39:18,025 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 创建默认策略参数服务成功
2025-07-13 14:39:18,031 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 已加载 3 个 OKX 交易对
2025-07-13 14:39:18,031 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 策略配置窗口初始化完成
2025-07-13 14:39:21,566 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 开始批量添加所有交易对的策略
2025-07-13 14:39:22,452 - 数据库.数据访问.类_机器人数据访问 - INFO - 添加机器人成功: BOT_1752388762_5863
2025-07-13 14:39:22,453 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 机器人数据保存成功: BOT_1752388762_5863
2025-07-13 14:39:22,453 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 成功添加策略: BTC/USDT
2025-07-13 14:39:22,459 - 数据库.数据访问.类_机器人数据访问 - INFO - 添加机器人成功: BOT_1752388762_1044
2025-07-13 14:39:22,459 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 机器人数据保存成功: BOT_1752388762_1044
2025-07-13 14:39:22,459 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 成功添加策略: ETH/USDT
2025-07-13 14:39:22,465 - 数据库.数据访问.类_机器人数据访问 - INFO - 添加机器人成功: BOT_1752388762_8759
2025-07-13 14:39:22,465 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 机器人数据保存成功: BOT_1752388762_8759
2025-07-13 14:39:22,465 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 成功添加策略: SOL/USDT
2025-07-13 14:39:22,465 - 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 - INFO - 批量添加完成！成功：3个，失败：0个
2025-07-13 14:39:22,469 - 窗口UI布局.UI管理.主窗口模块.类_策略控制管理 - INFO - 策略配置已保存
2025-07-13 14:39:25,088 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 7
2025-07-13 14:39:25,753 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 7
2025-07-13 14:39:28,446 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 5
2025-07-13 14:39:30,252 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 5
2025-07-13 14:39:30,860 - 窗口UI布局.UI管理.类_主窗口 - INFO - 选中策略: 7
2025-07-13 14:39:31,211 - 窗口UI布局.UI管理.主窗口模块.类_策略控制管理 - INFO - 开始修改机器人: BOT_1752388762_8759, 策略配置: AI策略
2025-07-13 14:39:31,212 - 窗口UI布局.UI管理.策略窗口模块.类_策略编辑窗口 - INFO - 创建默认策略参数服务成功
2025-07-13 14:39:31,230 - 窗口UI布局.UI管理.策略窗口模块.类_策略编辑窗口 - INFO - 设置AI策略参数成功
2025-07-13 14:39:31,231 - 窗口UI布局.UI管理.策略窗口模块.类_策略编辑窗口 - INFO - 机器人数据加载完成: BOT_1752388762_8759
2025-07-13 14:39:31,231 - 窗口UI布局.UI管理.策略窗口模块.类_策略编辑窗口 - INFO - 策略编辑窗口初始化完成，机器人编号: BOT_1752388762_8759
