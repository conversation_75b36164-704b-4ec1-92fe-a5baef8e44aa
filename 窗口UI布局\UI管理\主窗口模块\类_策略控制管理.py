# -*- coding: utf-8 -*-
"""
策略控制管理模块
负责处理策略的启动、停止、暂停等控制操作
"""

from PySide6.QtWidgets import QMessageBox, QDialog
from 窗口UI布局.UI管理.策略窗口模块.类_策略配置窗口 import 类_策略配置窗口
from 窗口UI布局.UI管理.策略窗口模块.类_策略编辑窗口 import 类_策略编辑窗口
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

class 类_策略控制管理:
    """
    策略控制管理类
    负责处理策略的启动、停止、暂停等控制操作
    """
    
    def __init__(self, 参_主窗口):
        """
        初始化策略控制管理器
        
        参数:
            参_主窗口: 主窗口实例
        """
        self.主窗口 = 参_主窗口
        logger.info("策略控制管理类初始化完成")
    
    def 启动所有策略(self):
        """
        启动所有策略的业务逻辑
        """
        logger.info("执行启动所有策略的业务逻辑")
        pass
    
    def 停止所有策略(self):
        """
        停止所有策略的业务逻辑
        """
        logger.info("执行停止所有策略的业务逻辑")
        pass
    
    def 暂停所有策略补单(self):
        """
        暂停所有策略补单的业务逻辑
        """
        logger.info("执行暂停所有策略补单的业务逻辑")
        pass
    
    def 恢复所有策略补单(self):
        """
        恢复所有策略补单的业务逻辑
        """
        logger.info("执行恢复所有策略补单的业务逻辑")
        pass
    
    def 执行一键平仓(self):
        """
        执行一键平仓的业务逻辑
        """
        logger.info("执行一键平仓的业务逻辑")
        pass
    
    def 单个启动(self, 参_行号):
        """
        启动单个机器人
        
        参数:
            参_行号: 表格行号
        """
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 23)  # 假设机器人编号在第24列
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            logger.info(f"启动机器人: {机器人编号}")
            self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 已启动")
    
    def 单个停止(self, 参_行号):
        """
        停止单个机器人
        
        参数:
            参_行号: 表格行号
        """
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 23)  # 假设机器人编号在第24列
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            logger.info(f"停止机器人: {机器人编号}")
            self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 已停止")
    
    def 单个暂停补单(self, 参_行号):
        """
        暂停单个机器人的补单功能
        
        参数:
            参_行号: 表格行号
        """
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 23)  # 假设机器人编号在第24列
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            logger.info(f"暂停机器人补单: {机器人编号}")
            self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 补单已暂停")
    
    def 单个恢复补单(self, 参_行号):
        """
        恢复单个机器人的补单功能
        
        参数:
            参_行号: 表格行号
        """
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 23)  # 假设机器人编号在第24列
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            logger.info(f"恢复机器人补单: {机器人编号}")
            self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 补单已恢复")
    
    def 单个平仓(self, 参_行号):
        """
        平仓单个机器人的所有持仓
        
        参数:
            参_行号: 表格行号
        """
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 23)  # 假设机器人编号在第24列
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            logger.info(f"准备平仓机器人: {机器人编号}")
            
            # 确认对话框
            局_回复 = QMessageBox.question(
                self.主窗口, "确认操作", 
                f"确定要平仓机器人 {机器人编号} 的所有持仓吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if 局_回复 == QMessageBox.StandardButton.Yes:
                logger.info(f"执行平仓机器人: {机器人编号}")
                self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 的所有持仓已平仓")
    
    def 单个删除(self, 参_行号):
        """
        删除单个机器人
        
        参数:
            参_行号: 表格行号
        """
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 23)  # 假设机器人编号在第24列
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            logger.info(f"准备删除机器人: {机器人编号}")
            
            # 确认对话框
            局_回复 = QMessageBox.warning(
                self.主窗口, "确认删除", 
                f"确定要删除机器人 {机器人编号} 吗？\n此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if 局_回复 == QMessageBox.StandardButton.Yes:
                # 从数据库中删除机器人
                if self.主窗口.类_数据库管理:
                    # 调用数据库管理器删除机器人
                    删除成功 = self.主窗口.类_数据库管理.删除机器人(机器人编号)
                    if not 删除成功:
                        logger.error(f"机器人 {机器人编号} 删除失败")
                        QMessageBox.critical(self.主窗口, "删除失败", f"机器人 {机器人编号} 删除失败！")
                        return
                
                # 从表格中移除该行
                self.主窗口.tableWidget_control.removeRow(参_行号)
                
                logger.info(f"机器人 {机器人编号} 删除成功")
                self.主窗口.显示状态栏消息(f"机器人 {机器人编号} 已删除")
    
    def 新建机器人(self):
        """
        新建机器人
        """
        logger.info("开始新建机器人")
        
        # 创建并显示策略配置窗口
        策略配置窗口 = 类_策略配置窗口(self.主窗口)
        
        # 以模态方式显示窗口
        if 策略配置窗口.exec() == QDialog.DialogCode.Accepted:
            logger.info("策略配置已保存")
            # 刷新表格数据
            self.主窗口.表格数据管理.刷新中控表格数据()
            self.主窗口.显示状态栏消息("新策略已创建")
    
    def 修改机器人(self, 参_行号):
        """
        修改机器人
        
        参数:
            参_行号: 表格行号
        """
        机器人编号项 = self.主窗口.tableWidget_control.item(参_行号, 23)  # 假设机器人编号在第24列
        if 机器人编号项:
            机器人编号 = 机器人编号项.text()
            
            # 获取策略配置类型
            策略配置项 = self.主窗口.tableWidget_control.item(参_行号, 19)  # 策略配置在第20列
            策略配置 = 策略配置项.text() if 策略配置项 else "未知"
            
            logger.info(f"开始修改机器人: {机器人编号}, 策略配置: {策略配置}")
            
            # 根据策略配置类型显示不同的提示
            if 策略配置 == "AI策略":
                print("这是AI策略，将显示AI策略编辑界面")
            elif 策略配置 == "自定义":
                print("这是自定义策略，将显示自定义策略编辑界面")
            else:
                print(f"未知的策略配置类型: {策略配置}")
            
            # 创建并显示策略编辑窗口
            策略编辑窗口 = 类_策略编辑窗口(机器人编号, self.主窗口)
            
            # 以模态方式显示窗口
            if 策略编辑窗口.exec() == QDialog.DialogCode.Accepted:
                print(f"策略 {机器人编号} 已更新")
                # 刷新表格数据
                self.主窗口.表格数据管理.刷新中控表格数据()
                self.主窗口.显示状态栏消息(f"策略 {机器人编号} 已更新") 